# AI 智能书签分类器

一款浏览器扩展，可使用您配置的 AI 模型自动为网站分类并创建书签。

## 功能

-   **单页分类**：当您浏览网页时，点击扩展图标，即可为当前页面进行智能分类。
-   **批量分类**：一键分类所有已打开的标签页，自动过滤特殊页面，支持进度显示和错误处理。
-   **智能文件夹管理**：自动将分类后的书签保存到浏览器书签栏的指定文件夹结构中（`AI分类书签 -> 分类名称 -> 书签`）。
-   **多API支持**：支持Google Generative Language API和OpenAI兼容的API服务。
-   **用户友好界面**：提供实时进度显示、详细状态信息和取消操作功能。
-   **自动关闭标签页**：可选择在成功添加书签后自动关闭对应的标签页，提高浏览效率。

## 安装指南

本扩展需要手动加载到您的浏览器中。

**Chrome 用户:**
1.  下载本扩展的所有文件并解压到一个文件夹中。
2.  打开 Chrome 浏览器，在地址栏输入 `chrome://extensions` 并回车。
3.  在页面右上角，启用“开发者模式”。
4.  点击左上角的“加载已解压的扩展程序”按钮。
5.  选择您在第一步解压的文件夹。

**Edge 用户:**
1.  下载本扩展的所有文件并解压到一个文件夹中。
2.  打开 Edge 浏览器，在地址栏输入 `edge://extensions` 并回车。
3.  在页面左下角，启用“开发人员模式”。
4.  点击“加载解压缩的”按钮。
5.  选择您在第一步解压的文件夹。

## 使用方法

1.  **配置 AI 服务:**
    *   点击浏览器工具栏上的“AI 智能书签分类器”扩展图标，打开设置弹窗。
    *   **API 地址:** 输入您的 AI 服务提供商的 API 端点 URL。
        *   *注意：本扩展目前期望一个与 OpenAI Chat Completions API 兼容的端点（即接受 POST 请求，JSON 体包含 `model` 和 `messages` 字段，并通过 `Authorization: Bearer <API_KEY>` 进行认证）。*
    *   **API 密钥:** 输入您的 API 密钥。
    *   **模型名称:** 输入您希望使用的具体 AI 模型名称（例如 `gpt-3.5-turbo`）。
    *   这些信息在您输入后会自动保存。

2.  **分类和收藏网站:**
    *   当您访问一个希望收藏和分类的网站时，点击扩展图标。
    *   确保顶部的 API 配置正确无误。
    *   点击“智能分类并收藏”按钮。
    *   扩展程序将使用您的 AI 配置来获取该网站的中文分类，并在您的书签中创建相应的文件夹和书签条目。
    *   您可以在弹窗中看到处理状态和结果。

## 书签结构

所有通过此扩展创建的书签将存放在名为 “AI分类书签” 的主文件夹下。每个唯一的 AI 生成分类都将在此主文件夹内拥有自己的子文件夹。

例如：
- AI分类书签
    - 新闻
        - [某新闻网站的书签]
    - 科技
        - [某科技博客的书签]
    - 购物
        - [某电商网站的书签]

## 批量分类功能

### 新增功能特性
- **一键批量处理**：可以同时处理所有已打开的标签页
- **智能过滤**：自动过滤掉特殊页面（chrome://、extension://等）
- **实时进度显示**：显示处理进度条和详细状态信息
- **错误处理**：单个标签页的错误不会影响其他标签页的处理
- **取消操作**：支持随时取消正在进行的批量操作
- **自动关闭标签页**：可选择在成功收藏后自动关闭对应的标签页

### 使用方法
1. 打开多个您想要分类的网页标签
2. 点击扩展图标
3. 确保API配置正确
4. **可选**：开启"成功收藏后自动关闭标签页"开关（如果希望自动清理已收藏的标签页）
5. 点击"批量分类所有标签页"按钮
6. 观察进度条和详细处理状态
7. 等待处理完成或随时取消操作

### 性能优化
- 自动添加延迟避免API限制
- 批量处理时显示详细的成功/失败统计
- 支持大量标签页的高效处理

## 注意事项

-   请确保您提供的 API 地址、密钥和模型名称正确无误，并且您的 AI 服务账户有足够的配额。
-   API 密钥将存储在浏览器的同步存储中，请注意其安全性。
-   如果遇到分类不准确的情况，可以尝试调整提示或使用的 AI 模型（如果您的 AI 服务支持）。本扩展使用的默认提示是请求一个简洁的中文分类名称。
-   批量处理时建议不要同时打开过多标签页，以避免API配额快速消耗。
-   批量处理过程中请保持扩展弹窗打开，以便查看处理进度。
