document.addEventListener('DOMContentLoaded', () => {
  const apiUrlInput = document.getElementById('apiUrl');
  const apiKeyInput = document.getElementById('apiKey');
  const modelNameInput = document.getElementById('modelName');
  const classifyBtn = document.getElementById('classifyBtn');
  const statusMessageDiv = document.getElementById('statusMessage');

  // 1. Load Configuration on Startup
  chrome.storage.sync.get(['apiUrl', 'apiKey', 'modelName'], (result) => {
    if (result.apiUrl) {
      apiUrlInput.value = result.apiUrl;
    }
    if (result.apiKey) {
      apiKeyInput.value = result.apiKey;
    }
    if (result.modelName) {
      modelNameInput.value = result.modelName;
    }
  });

  // 2. <PERSON>le "Classify" Button Click
  classifyBtn.addEventListener('click', () => {
    const apiUrl = apiUrlInput.value.trim();
    const apiKey = apiKeyInput.value.trim();
    const modelName = modelNameInput.value.trim();

    // Save Configuration
    chrome.storage.sync.set({ apiUrl, apiKey, modelName }, () => {
      if (chrome.runtime.lastError) {
        statusMessageDiv.textContent = '配置保存失败: ' + chrome.runtime.lastError.message;
      } else {
        // Briefly show saved message, then proceed or show other messages
        // statusMessageDiv.textContent = '配置已保存！';
        // setTimeout(() => { if (statusMessageDiv.textContent === '配置已保存！') statusMessageDiv.textContent = ''; }, 2000);
      }
    });

    // Validate Inputs
    if (!apiUrl || !apiKey) {
      statusMessageDiv.textContent = 'API 地址和密钥不能为空！';
      return;
    }

    statusMessageDiv.textContent = '正在获取当前标签页信息...';

    // Send Message to Background Script
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs && tabs.length > 0) {
        const currentTab = tabs[0];
        if (!currentTab.id) {
            statusMessageDiv.textContent = '无法获取当前标签页ID。';
            return;
        }
        if (!currentTab.url || currentTab.url.startsWith('chrome://')) {
            statusMessageDiv.textContent = '无法对特殊页面或空白页进行分类。';
            return;
        }

        statusMessageDiv.textContent = '正在分类...';
        chrome.runtime.sendMessage({
          action: "classifyAndBookmark",
          config: {
            apiUrl: apiUrl,
            apiKey: apiKey,
            modelName: modelName
          },
          tab: {
            id: currentTab.id,
            url: currentTab.url,
            title: currentTab.title
          }
        }, response => {
          if (chrome.runtime.lastError) {
            // Handle errors from sendMessage, e.g., if the background script isn't ready
            statusMessageDiv.textContent = '发送消息到后台脚本失败: ' + chrome.runtime.lastError.message;
          }
          // Response from background script will be handled by chrome.runtime.onMessage
        });
      } else {
        statusMessageDiv.textContent = '无法获取当前活动标签页。';
      }
    });
  });

  // 3. Listen for Messages from Background Script
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.statusUpdate) {
      statusMessageDiv.textContent = request.statusUpdate;
      // Optionally, clear the message after a few seconds
      // setTimeout(() => { if (statusMessageDiv.textContent === request.statusUpdate) statusMessageDiv.textContent = ''; }, 7000);
    }
    // sendResponse({}); // Indicate that the message was received, if needed
    return true; // Required if sendResponse will be called asynchronously.
  });
});
