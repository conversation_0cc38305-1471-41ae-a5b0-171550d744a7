document.addEventListener('DOMContentLoaded', () => {
  const apiUrlInput = document.getElementById('apiUrl');
  const apiKeyInput = document.getElementById('apiKey');
  const modelNameInput = document.getElementById('modelName');
  const autoCloseTabCheckbox = document.getElementById('autoCloseTab');
  const classifyBtn = document.getElementById('classifyBtn');
  const batchClassifyBtn = document.getElementById('batchClassifyBtn');
  const cancelBtn = document.getElementById('cancelBtn');
  const statusMessageDiv = document.getElementById('statusMessage');
  const batchProgressDiv = document.getElementById('batchProgress');
  const progressText = document.getElementById('progressText');
  const progressFill = document.getElementById('progressFill');
  const batchDetails = document.getElementById('batchDetails');

  // 批量处理状态
  let isBatchProcessing = false;
  let batchCancelled = false;

  // 1. Load Configuration on Startup
  chrome.storage.sync.get(['apiUrl', 'apiKey', 'modelName', 'autoCloseTab'], (result) => {
    if (result.apiUrl) {
      apiUrlInput.value = result.apiUrl;
    }
    if (result.apiKey) {
      apiKeyInput.value = result.apiKey;
    }
    if (result.modelName) {
      modelNameInput.value = result.modelName;
    }
    // 设置自动关闭标签页开关状态，默认为false
    autoCloseTabCheckbox.checked = result.autoCloseTab || false;
  });

  // 1.5. Handle Auto Close Tab Switch Change
  autoCloseTabCheckbox.addEventListener('change', () => {
    const autoCloseTab = autoCloseTabCheckbox.checked;
    console.log('Auto close tab switch changed to:', autoCloseTab);
    // 立即保存开关状态
    chrome.storage.sync.set({ autoCloseTab }, () => {
      if (chrome.runtime.lastError) {
        console.error('Failed to save autoCloseTab setting:', chrome.runtime.lastError);
      } else {
        console.log('Auto close tab setting saved:', autoCloseTab);
      }
    });
  });

  // 2. Handle "Classify" Button Click
  classifyBtn.addEventListener('click', () => {
    const apiUrl = apiUrlInput.value.trim();
    const apiKey = apiKeyInput.value.trim();
    const modelName = modelNameInput.value.trim();
    const autoCloseTab = autoCloseTabCheckbox.checked;

    // Save Configuration
    chrome.storage.sync.set({ apiUrl, apiKey, modelName, autoCloseTab }, () => {
      if (chrome.runtime.lastError) {
        statusMessageDiv.textContent = '配置保存失败: ' + chrome.runtime.lastError.message;
      } else {
        // Briefly show saved message, then proceed or show other messages
        // statusMessageDiv.textContent = '配置已保存！';
        // setTimeout(() => { if (statusMessageDiv.textContent === '配置已保存！') statusMessageDiv.textContent = ''; }, 2000);
      }
    });

    // Validate Inputs
    if (!apiUrl || !apiKey) {
      statusMessageDiv.textContent = 'API 地址和密钥不能为空！';
      return;
    }

    statusMessageDiv.textContent = '正在获取当前标签页信息...';

    // Send Message to Background Script
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs && tabs.length > 0) {
        const currentTab = tabs[0];
        if (!currentTab.id) {
            statusMessageDiv.textContent = '无法获取当前标签页ID。';
            return;
        }
        if (!currentTab.url || currentTab.url.startsWith('chrome://')) {
            statusMessageDiv.textContent = '无法对特殊页面或空白页进行分类。';
            return;
        }

        statusMessageDiv.textContent = '正在分类...';
        console.log('Sending classify message with autoCloseTab:', autoCloseTab);
        chrome.runtime.sendMessage({
          action: "classifyAndBookmark",
          config: {
            apiUrl: apiUrl,
            apiKey: apiKey,
            modelName: modelName,
            autoCloseTab: autoCloseTab
          },
          tab: {
            id: currentTab.id,
            url: currentTab.url,
            title: currentTab.title
          }
        }, response => {
          if (chrome.runtime.lastError) {
            // Handle errors from sendMessage, e.g., if the background script isn't ready
            statusMessageDiv.textContent = '发送消息到后台脚本失败: ' + chrome.runtime.lastError.message;
          }
          // Response from background script will be handled by chrome.runtime.onMessage
        });
      } else {
        statusMessageDiv.textContent = '无法获取当前活动标签页。';
      }
    });
  });

  // 2.5. Handle "Batch Classify" Button Click
  batchClassifyBtn.addEventListener('click', async () => {
    const apiUrl = apiUrlInput.value.trim();
    const apiKey = apiKeyInput.value.trim();
    const modelName = modelNameInput.value.trim();
    const autoCloseTab = autoCloseTabCheckbox.checked;

    // Validate Inputs
    if (!apiUrl || !apiKey) {
      statusMessageDiv.textContent = 'API 地址和密钥不能为空！';
      return;
    }

    // Save Configuration
    chrome.storage.sync.set({ apiUrl, apiKey, modelName, autoCloseTab });

    // Start batch processing
    await startBatchClassification({ apiUrl, apiKey, modelName, autoCloseTab });
  });

  // 2.6. Handle "Cancel" Button Click
  cancelBtn.addEventListener('click', () => {
    batchCancelled = true;
    statusMessageDiv.textContent = '正在取消批量操作...';
    resetBatchUI();
  });

  // 3. Listen for Messages from Background Script
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.statusUpdate) {
      statusMessageDiv.textContent = request.statusUpdate;
      // Optionally, clear the message after a few seconds
      // setTimeout(() => { if (statusMessageDiv.textContent === request.statusUpdate) statusMessageDiv.textContent = ''; }, 7000);
    }

    // 处理批量进度更新
    if (request.batchProgress) {
      const progress = request.batchProgress;
      updateProgress(progress.current, progress.total);

      if (progress.status === 'processing') {
        addBatchDetail(`正在处理: ${progress.currentTab}`, 'processing');
      } else if (progress.status === 'success') {
        addBatchDetail(`✅ ${progress.currentTab} → ${progress.category}`, 'success');
      } else if (progress.status === 'error') {
        addBatchDetail(`❌ ${progress.currentTab}: ${progress.error}`, 'error');
      }
    }

    // 处理批量完成消息
    if (request.batchComplete) {
      const result = request.batchComplete;
      statusMessageDiv.textContent = `批量处理完成！成功: ${result.successful}, 失败: ${result.failed}`;
      isBatchProcessing = false;
      hideButtons(false);
    }

    // sendResponse({}); // Indicate that the message was received, if needed
    return true; // Required if sendResponse will be called asynchronously.
  });

  // 批量处理函数
  async function startBatchClassification(config) {
    if (isBatchProcessing) {
      statusMessageDiv.textContent = '批量处理正在进行中...';
      return;
    }

    isBatchProcessing = true;
    batchCancelled = false;

    try {
      // 获取所有标签页
      statusMessageDiv.textContent = '正在获取所有标签页...';
      const allTabs = await new Promise((resolve) => {
        chrome.tabs.query({}, resolve);
      });

      // 过滤掉特殊页面
      const validTabs = allTabs.filter(tab =>
        tab.url &&
        tab.url.startsWith('http') &&
        !tab.url.startsWith('chrome://') &&
        !tab.url.startsWith('chrome-extension://') &&
        !tab.url.startsWith('edge://') &&
        !tab.url.startsWith('about:') &&
        tab.title &&
        tab.title.trim() !== ''
      );

      if (validTabs.length === 0) {
        statusMessageDiv.textContent = '没有找到可以分类的标签页。';
        resetBatchUI();
        return;
      }

      // 显示批量处理UI
      showBatchUI(validTabs.length);

      statusMessageDiv.textContent = `开始批量处理 ${validTabs.length} 个标签页...`;

      // 发送批量处理请求到background script
      const response = await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({
          action: "batchClassifyAndBookmark",
          config: config,
          tabs: validTabs.map(tab => ({
            id: tab.id,
            url: tab.url,
            title: tab.title
          }))
        }, response => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response);
          }
        });
      });

      if (!response.success) {
        throw new Error(response.error || '批量处理失败');
      }

    } catch (error) {
      statusMessageDiv.textContent = `批量处理出错: ${error.message}`;
    } finally {
      isBatchProcessing = false;
      hideButtons(false);
    }
  }

  function showBatchUI(totalTabs) {
    batchProgressDiv.style.display = 'block';
    progressText.textContent = `0/${totalTabs}`;
    progressFill.style.width = '0%';
    batchDetails.innerHTML = '';
    hideButtons(true);
  }

  function updateProgress(current, total) {
    const percentage = (current / total) * 100;
    progressText.textContent = `${current}/${total}`;
    progressFill.style.width = `${percentage}%`;
  }

  function addBatchDetail(message, type) {
    const item = document.createElement('div');
    item.className = `batch-item ${type}`;
    item.textContent = message;
    batchDetails.appendChild(item);
    batchDetails.scrollTop = batchDetails.scrollHeight;
  }

  function hideButtons(hide) {
    if (hide) {
      classifyBtn.style.display = 'none';
      batchClassifyBtn.style.display = 'none';
      cancelBtn.style.display = 'block';
    } else {
      classifyBtn.style.display = 'block';
      batchClassifyBtn.style.display = 'block';
      cancelBtn.style.display = 'none';
    }
  }

  function resetBatchUI() {
    setTimeout(() => {
      batchProgressDiv.style.display = 'none';
      hideButtons(false);
      isBatchProcessing = false;
    }, 2000);
  }
});
