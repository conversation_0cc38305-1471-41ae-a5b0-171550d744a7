const MAIN_BOOKMARK_FOLDER_TITLE = "AI分类书签";

// 1. Listen for Messages from popup.js
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "classifyAndBookmark") {
    console.log("Received action:", request.action);
    const config = request.config;
    const tab = request.tab;

    console.log("Received for classification:", tab.title, tab.url);
    console.log("AI Config:", config);

    handleClassification(config, tab, tab.id)
      .then(() => {
        // Async operations within handleClassification will send their own messages.
      })
      .catch(error => {
        console.error("Error in handleClassification chain:", error);
        chrome.runtime.sendMessage({ statusUpdate: `处理失败: ${error.message || "未知错误"}` });
      });

    return true; // Indicate that we will send a response asynchronously
  }
  return false;
});

// Revised getOrCreateFolder function
async function getOrCreateFolder(folderTitle, parentId) {
  try {
    let existingFolder = null;
    if (parentId) {
      const children = await chrome.bookmarks.getChildren(parentId);
      existingFolder = children.find(child => !child.url && child.title === folderTitle);
    } else {
      const searchResults = await chrome.bookmarks.search({ title: folderTitle });
      existingFolder = searchResults.find(item => !item.url && item.title === folderTitle);
    }

    if (existingFolder) {
      console.log(`Folder "${folderTitle}" found with id ${existingFolder.id}`);
      return existingFolder;
    } else {
      console.log(`Folder "${folderTitle}" not found under parent "${parentId || 'root'}". Creating...`);
      const newFolder = await chrome.bookmarks.create({
        title: folderTitle,
        parentId: parentId
      });
      console.log(`Folder "${folderTitle}" created with id ${newFolder.id}`);
      return newFolder;
    }
  } catch (e) {
    console.error(`Error getting or creating folder "${folderTitle}":`, e.toString());
    throw new Error(`操作文件夹 "${folderTitle}" 时出错: ${e.message}`);
  }
}

// 3. Implement handleClassification Function
async function handleClassification(config, tabInfo, tabId) {
  try {
    chrome.runtime.sendMessage({ statusUpdate: `正在为 "${tabInfo.title}" 获取分类...` });

    const category = await callAI(tabInfo.title, config);

    if (category) {
      console.log("AI categorized as:", category);
      chrome.runtime.sendMessage({ statusUpdate: `网站 "${tabInfo.title}" AI分类为: ${category}` });

      if (!tabInfo.url || !tabInfo.url.startsWith("http")) {
        console.warn(`Skipping bookmark for non-HTTP URL: ${tabInfo.url}`);
        chrome.runtime.sendMessage({ statusUpdate: `无法为 "${tabInfo.title}"（非网页链接）创建书签。` });
        return;
      }

      let mainFolder, categoryFolder;
      try {
        mainFolder = await getOrCreateFolder(MAIN_BOOKMARK_FOLDER_TITLE);
      } catch (e) {
        chrome.runtime.sendMessage({ statusUpdate: `创建/查找主文件夹 "${MAIN_BOOKMARK_FOLDER_TITLE}" 失败: ${e.message}` });
        return;
      }

      if (!mainFolder || !mainFolder.id) {
        chrome.runtime.sendMessage({ statusUpdate: `未能获取主书签文件夹 "${MAIN_BOOKMARK_FOLDER_TITLE}"。` });
        return;
      }

      try {
        categoryFolder = await getOrCreateFolder(category, mainFolder.id);
      } catch (e) {
        chrome.runtime.sendMessage({ statusUpdate: `创建/查找分类文件夹 "${category}" 失败: ${e.message}` });
        return;
      }

      if (!categoryFolder || !categoryFolder.id) {
          chrome.runtime.sendMessage({ statusUpdate: `未能获取分类文件夹 "${category}"。` });
          return;
      }

      try {
        await chrome.bookmarks.create({
          parentId: categoryFolder.id,
          title: tabInfo.title,
          url: tabInfo.url
        });
        const successMsg = `网站 "${tabInfo.title}" 已分类到 "${category}" 并成功收藏。`;
        console.log(`Bookmark created for ${tabInfo.title} in ${category}`);
        chrome.runtime.sendMessage({ statusUpdate: successMsg });
      } catch (e) {
        console.error(`Error creating bookmark for "${tabInfo.title}":`, e);
        chrome.runtime.sendMessage({ statusUpdate: `为 "${tabInfo.title}" 创建书签失败: ${e.message}` });
      }

    } else {
      // This case should ideally be caught by errors within callAI if category is null/undefined
      console.error("AI classification returned no category for:", tabInfo.title);
      chrome.runtime.sendMessage({ statusUpdate: `AI 分类未能确定类别: ${tabInfo.title}` });
    }
  } catch (error) {
    console.error("AI classification or subsequent bookmarking process failed:", error);
    // error.message should be the user-friendly message from callAI or getOrCreateFolder
    chrome.runtime.sendMessage({ statusUpdate: `处理失败: ${error.message || "未知错误"}` });
  }
}

// 4. Implement REAL callAI Function
async function callAI(websiteTitle, aiConfig) {
  console.log("Attempting real AI call for title:", websiteTitle, "with config:", aiConfig);

  if (!aiConfig.apiUrl || !aiConfig.apiKey || !aiConfig.modelName) {
    throw new Error("API 地址、API 密钥或模型名称未配置完整。");
  }

  const userPrompt = `请为以下网站标题提供一个简洁的中文分类名称（例如：新闻、科技、购物、教育、娱乐等）。请只返回1到3个字的分类名称，不要任何其他解释、标点符号或句子。网站标题：\"${websiteTitle}\"`;

  // 检测API类型并构造相应的请求
  let payload, headers, apiUrl;

  if (aiConfig.apiUrl.includes('googleapis.com')) {
    // Google Generative Language API 格式
    apiUrl = `${aiConfig.apiUrl}/v1beta/models/${aiConfig.modelName}:generateContent?key=${aiConfig.apiKey}`;
    payload = {
      contents: [{
        parts: [{
          text: userPrompt
        }]
      }],
      generationConfig: {
        maxOutputTokens: 15,
        temperature: 0.3
      }
    };
    headers = {
      'Content-Type': 'application/json'
    };
  } else {
    // OpenAI 兼容格式 (默认)
    apiUrl = aiConfig.apiUrl;
    payload = {
      model: aiConfig.modelName,
      messages: [
        { role: "user", content: userPrompt }
      ],
      max_tokens: 15,
      temperature: 0.3
    };
    headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${aiConfig.apiKey}`
    };
  }

  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorBody = await response.text();
      console.error("AI API Error Response Body:", errorBody);
      throw new Error(`AI API 请求失败，状态码：${response.status}. ${errorBody}`);
    }

    const data = await response.json();
    console.log("AI API Response:", JSON.stringify(data, null, 2));

    let category;

    if (aiConfig.apiUrl.includes('googleapis.com')) {
      // Google API 响应格式
      category = data.candidates && data.candidates[0] && data.candidates[0].content &&
                 data.candidates[0].content.parts && data.candidates[0].content.parts[0] &&
                 data.candidates[0].content.parts[0].text;
    } else {
      // OpenAI 兼容格式
      category = data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content;
    }

    if (!category) {
      console.error("Could not extract category from AI response:", JSON.stringify(data));
      throw new Error("未能从 AI 响应中提取分类名称。请检查API响应结构。");
    }

    category = category.trim().replace(/[\r\n".“”。！，、；：?!,;:]/g, '');

    if (category.length > 20) {
       console.warn(`Original category "${category}" too long, truncating.`);
       category = category.substring(0, 20);
    }
    if (!category) { // Check if category became empty after trimming/replacing
        console.error("Category became empty after cleaning:", JSON.stringify(data));
        throw new Error("AI返回的分类名称处理后为空。");
    }

    console.log("AI suggested category:", category);
    return category;

  } catch (error) {
    console.error("callAI Error:", error);
    if (error.message.startsWith("API 地址、API 密钥或模型名称未配置完整。") ||
        error.message.startsWith("AI API 请求失败") ||
        error.message.startsWith("未能从 AI 响应中提取分类名称") ||
        error.message.startsWith("AI返回的分类名称处理后为空")) {
      throw error; // Rethrow specific, user-facing errors
    }
    // For generic errors (network, JSON parsing, etc.)
    throw new Error(`调用 AI 服务时出错: ${error.message}`);
  }
}
