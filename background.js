const MAIN_BOOKMARK_FOLDER_TITLE = "AI分类书签";

// 1. Listen for Messages from popup.js
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "classifyAndBookmark") {
    console.log("Received action:", request.action);
    const config = request.config;
    const tab = request.tab;

    console.log("Received for classification:", tab.title, tab.url);
    console.log("AI Config:", config);

    handleClassification(config, tab, tab.id)
      .then(() => {
        // Async operations within handleClassification will send their own messages.
        sendResponse({ success: true });
      })
      .catch(error => {
        console.error("Error in handleClassification chain:", error);
        chrome.runtime.sendMessage({ statusUpdate: `处理失败: ${error.message || "未知错误"}` });
        sendResponse({ success: false, error: error.message });
      });

    return true; // Indicate that we will send a response asynchronously
  }

  if (request.action === "batchClassifyAndBookmark") {
    console.log("Received batch action:", request.action);
    const config = request.config;
    const tabs = request.tabs;

    handleBatchClassification(config, tabs)
      .then((results) => {
        sendResponse({ success: true, results: results });
      })
      .catch(error => {
        console.error("Error in batch classification:", error);
        sendResponse({ success: false, error: error.message });
      });

    return true; // Indicate that we will send a response asynchronously
  }

  return false;
});

// Revised getOrCreateFolder function
async function getOrCreateFolder(folderTitle, parentId) {
  try {
    let existingFolder = null;
    if (parentId) {
      const children = await chrome.bookmarks.getChildren(parentId);
      existingFolder = children.find(child => !child.url && child.title === folderTitle);
    } else {
      const searchResults = await chrome.bookmarks.search({ title: folderTitle });
      existingFolder = searchResults.find(item => !item.url && item.title === folderTitle);
    }

    if (existingFolder) {
      console.log(`Folder "${folderTitle}" found with id ${existingFolder.id}`);
      return existingFolder;
    } else {
      console.log(`Folder "${folderTitle}" not found under parent "${parentId || 'root'}". Creating...`);
      const newFolder = await chrome.bookmarks.create({
        title: folderTitle,
        parentId: parentId
      });
      console.log(`Folder "${folderTitle}" created with id ${newFolder.id}`);
      return newFolder;
    }
  } catch (e) {
    console.error(`Error getting or creating folder "${folderTitle}":`, e.toString());
    throw new Error(`操作文件夹 "${folderTitle}" 时出错: ${e.message}`);
  }
}

// 3. Implement handleClassification Function
async function handleClassification(config, tabInfo, tabId) {
  try {
    chrome.runtime.sendMessage({ statusUpdate: `正在为 "${tabInfo.title}" 获取分类...` });

    const category = await callAI(tabInfo.title, config);

    if (category) {
      console.log("AI categorized as:", category);
      chrome.runtime.sendMessage({ statusUpdate: `网站 "${tabInfo.title}" AI分类为: ${category}` });

      if (!tabInfo.url || !tabInfo.url.startsWith("http")) {
        console.warn(`Skipping bookmark for non-HTTP URL: ${tabInfo.url}`);
        chrome.runtime.sendMessage({ statusUpdate: `无法为 "${tabInfo.title}"（非网页链接）创建书签。` });
        return;
      }

      let mainFolder, categoryFolder;
      try {
        mainFolder = await getOrCreateFolder(MAIN_BOOKMARK_FOLDER_TITLE);
      } catch (e) {
        chrome.runtime.sendMessage({ statusUpdate: `创建/查找主文件夹 "${MAIN_BOOKMARK_FOLDER_TITLE}" 失败: ${e.message}` });
        return;
      }

      if (!mainFolder || !mainFolder.id) {
        chrome.runtime.sendMessage({ statusUpdate: `未能获取主书签文件夹 "${MAIN_BOOKMARK_FOLDER_TITLE}"。` });
        return;
      }

      try {
        categoryFolder = await getOrCreateFolder(category, mainFolder.id);
      } catch (e) {
        chrome.runtime.sendMessage({ statusUpdate: `创建/查找分类文件夹 "${category}" 失败: ${e.message}` });
        return;
      }

      if (!categoryFolder || !categoryFolder.id) {
          chrome.runtime.sendMessage({ statusUpdate: `未能获取分类文件夹 "${category}"。` });
          return;
      }

      try {
        await chrome.bookmarks.create({
          parentId: categoryFolder.id,
          title: tabInfo.title,
          url: tabInfo.url
        });
        const successMsg = `网站 "${tabInfo.title}" 已分类到 "${category}" 并成功收藏。`;
        console.log(`Bookmark created for ${tabInfo.title} in ${category}`);
        chrome.runtime.sendMessage({ statusUpdate: successMsg });

        // 如果开启了自动关闭标签页功能，则关闭当前标签页
        console.log(`Auto close tab setting: ${config.autoCloseTab}, Tab ID: ${tabId}`);
        if (config.autoCloseTab && tabId) {
          try {
            // 添加短暂延迟，确保状态消息能够显示
            setTimeout(async () => {
              try {
                await chrome.tabs.remove(tabId);
                console.log(`Tab ${tabId} closed automatically after bookmarking`);
              } catch (closeError) {
                console.warn(`Failed to close tab ${tabId}:`, closeError);
              }
            }, 1500); // 1.5秒延迟，让用户看到成功消息
            chrome.runtime.sendMessage({ statusUpdate: `${successMsg} 标签页将在1.5秒后自动关闭。` });
          } catch (closeError) {
            console.warn(`Failed to setup tab close:`, closeError);
            chrome.runtime.sendMessage({ statusUpdate: `书签已创建，但设置自动关闭失败: ${closeError.message}` });
          }
        } else {
          console.log(`Auto close tab disabled or no tab ID available`);
        }
      } catch (e) {
        console.error(`Error creating bookmark for "${tabInfo.title}":`, e);
        chrome.runtime.sendMessage({ statusUpdate: `为 "${tabInfo.title}" 创建书签失败: ${e.message}` });
      }

    } else {
      // This case should ideally be caught by errors within callAI if category is null/undefined
      console.error("AI classification returned no category for:", tabInfo.title);
      chrome.runtime.sendMessage({ statusUpdate: `AI 分类未能确定类别: ${tabInfo.title}` });
    }
  } catch (error) {
    console.error("AI classification or subsequent bookmarking process failed:", error);
    // error.message should be the user-friendly message from callAI or getOrCreateFolder
    chrome.runtime.sendMessage({ statusUpdate: `处理失败: ${error.message || "未知错误"}` });
  }
}

// 4. Implement REAL callAI Function
async function callAI(websiteTitle, aiConfig) {
  console.log("Attempting real AI call for title:", websiteTitle, "with config:", aiConfig);

  if (!aiConfig.apiUrl || !aiConfig.apiKey || !aiConfig.modelName) {
    throw new Error("API 地址、API 密钥或模型名称未配置完整。");
  }

  const userPrompt = `请为以下网站标题提供一个简洁的中文分类名称（例如：新闻、科技、购物、教育、娱乐等）。请只返回1到3个字的分类名称，不要任何其他解释、标点符号或句子。网站标题：\"${websiteTitle}\"`;

  // 检测API类型并构造相应的请求
  let payload, headers, apiUrl;

  if (aiConfig.apiUrl.includes('googleapis.com')) {
    // Google Generative Language API 格式
    apiUrl = `${aiConfig.apiUrl}/v1beta/models/${aiConfig.modelName}:generateContent?key=${aiConfig.apiKey}`;
    payload = {
      contents: [{
        parts: [{
          text: userPrompt
        }]
      }],
      generationConfig: {
        maxOutputTokens: 15,
        temperature: 0.3
      }
    };
    headers = {
      'Content-Type': 'application/json'
    };
  } else {
    // OpenAI 兼容格式 (默认)
    apiUrl = aiConfig.apiUrl;
    payload = {
      model: aiConfig.modelName,
      messages: [
        { role: "user", content: userPrompt }
      ],
      max_tokens: 15,
      temperature: 0.3
    };
    headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${aiConfig.apiKey}`
    };
  }

  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorBody = await response.text();
      console.error("AI API Error Response Body:", errorBody);
      throw new Error(`AI API 请求失败，状态码：${response.status}. ${errorBody}`);
    }

    const data = await response.json();
    console.log("AI API Response:", JSON.stringify(data, null, 2));

    let category;

    if (aiConfig.apiUrl.includes('googleapis.com')) {
      // Google API 响应格式
      category = data.candidates && data.candidates[0] && data.candidates[0].content &&
                 data.candidates[0].content.parts && data.candidates[0].content.parts[0] &&
                 data.candidates[0].content.parts[0].text;
    } else {
      // OpenAI 兼容格式
      category = data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content;
    }

    if (!category) {
      console.error("Could not extract category from AI response:", JSON.stringify(data));
      throw new Error("未能从 AI 响应中提取分类名称。请检查API响应结构。");
    }

    category = category.trim().replace(/[\r\n".“”。！，、；：?!,;:]/g, '');

    if (category.length > 20) {
       console.warn(`Original category "${category}" too long, truncating.`);
       category = category.substring(0, 20);
    }
    if (!category) { // Check if category became empty after trimming/replacing
        console.error("Category became empty after cleaning:", JSON.stringify(data));
        throw new Error("AI返回的分类名称处理后为空。");
    }

    console.log("AI suggested category:", category);
    return category;

  } catch (error) {
    console.error("callAI Error:", error);
    if (error.message.startsWith("API 地址、API 密钥或模型名称未配置完整。") ||
        error.message.startsWith("AI API 请求失败") ||
        error.message.startsWith("未能从 AI 响应中提取分类名称") ||
        error.message.startsWith("AI返回的分类名称处理后为空")) {
      throw error; // Rethrow specific, user-facing errors
    }
    // For generic errors (network, JSON parsing, etc.)
    throw new Error(`调用 AI 服务时出错: ${error.message}`);
  }
}

// 5. Implement Batch Classification Function
async function handleBatchClassification(config, tabs) {
  const results = [];
  let mainFolder;

  try {
    // 预先创建主文件夹
    mainFolder = await getOrCreateFolder(MAIN_BOOKMARK_FOLDER_TITLE);
    if (!mainFolder || !mainFolder.id) {
      throw new Error(`无法创建或获取主书签文件夹 "${MAIN_BOOKMARK_FOLDER_TITLE}"`);
    }
  } catch (error) {
    throw new Error(`初始化主文件夹失败: ${error.message}`);
  }

  for (let i = 0; i < tabs.length; i++) {
    const tab = tabs[i];
    const result = {
      tab: tab,
      success: false,
      error: null,
      category: null
    };

    try {
      console.log(`Processing tab ${i + 1}/${tabs.length}: ${tab.title}`);

      // 发送进度更新
      chrome.runtime.sendMessage({
        batchProgress: {
          current: i + 1,
          total: tabs.length,
          currentTab: tab.title,
          status: 'processing'
        }
      });

      // 检查URL有效性
      if (!tab.url || !tab.url.startsWith("http")) {
        throw new Error("非有效的网页链接");
      }

      // 调用AI分类
      const category = await callAI(tab.title, config);

      if (!category) {
        throw new Error("AI未能返回有效分类");
      }

      result.category = category;

      // 创建分类文件夹
      let categoryFolder;
      try {
        categoryFolder = await getOrCreateFolder(category, mainFolder.id);
      } catch (e) {
        throw new Error(`创建分类文件夹失败: ${e.message}`);
      }

      if (!categoryFolder || !categoryFolder.id) {
        throw new Error(`无法获取分类文件夹 "${category}"`);
      }

      // 创建书签
      await chrome.bookmarks.create({
        parentId: categoryFolder.id,
        title: tab.title,
        url: tab.url
      });

      result.success = true;
      console.log(`Successfully processed: ${tab.title} -> ${category}`);

      // 如果开启了自动关闭标签页功能，则关闭当前标签页
      console.log(`Batch: Auto close tab setting: ${config.autoCloseTab}, Tab ID: ${tab.id}`);
      if (config.autoCloseTab && tab.id) {
        try {
          // 为批量处理添加小延迟，避免同时关闭太多标签页
          setTimeout(async () => {
            try {
              await chrome.tabs.remove(tab.id);
              console.log(`Batch: Tab ${tab.id} closed automatically after bookmarking`);
            } catch (closeError) {
              console.warn(`Batch: Failed to close tab ${tab.id}:`, closeError);
            }
          }, 200 * i); // 每个标签页延迟200ms * 索引，错开关闭时间
        } catch (closeError) {
          console.warn(`Batch: Failed to setup tab close for ${tab.id}:`, closeError);
        }
      } else {
        console.log(`Batch: Auto close tab disabled or no tab ID available`);
      }

      // 发送成功消息
      chrome.runtime.sendMessage({
        batchProgress: {
          current: i + 1,
          total: tabs.length,
          currentTab: tab.title,
          status: 'success',
          category: category
        }
      });

    } catch (error) {
      result.error = error.message;
      console.error(`Failed to process tab "${tab.title}":`, error);

      // 发送错误消息
      chrome.runtime.sendMessage({
        batchProgress: {
          current: i + 1,
          total: tabs.length,
          currentTab: tab.title,
          status: 'error',
          error: error.message
        }
      });
    }

    results.push(result);

    // 添加延迟避免API限制
    if (i < tabs.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  // 发送完成消息
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;

  chrome.runtime.sendMessage({
    batchComplete: {
      total: tabs.length,
      successful: successful,
      failed: failed,
      results: results
    }
  });

  return results;
}
