body {
  width: 350px;
  font-family: sans-serif;
  padding: 10px;
}

label {
  display: block;
  margin-top: 10px;
  font-size: 0.9em;
}

input[type='url'],
input[type='password'],
input[type='text'] {
  width: 95%;
  padding: 8px;
  margin-top: 3px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

button {
  display: block;
  width: 100%;
  padding: 10px;
  margin-top: 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
}

button:hover {
  background-color: #0056b3;
}

#batchClassifyBtn {
  background-color: #28a745;
  margin-top: 10px;
}

#batchClassifyBtn:hover {
  background-color: #218838;
}

#cancelBtn {
  background-color: #dc3545;
  margin-top: 10px;
}

#cancelBtn:hover {
  background-color: #c82333;
}

#statusMessage {
  font-size: 0.9em;
  color: red;
  margin-top: 10px;
  min-height: 20px;
}

/* 批量处理进度样式 */
#batchProgress {
  margin-top: 15px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.9em;
  font-weight: bold;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background-color: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
}

#progressFill {
  height: 100%;
  background-color: #007bff;
  width: 0%;
  transition: width 0.3s ease;
}

.batch-details {
  max-height: 150px;
  overflow-y: auto;
  font-size: 0.8em;
  line-height: 1.4;
}

.batch-item {
  padding: 2px 0;
  border-bottom: 1px solid #eee;
}

.batch-item:last-child {
  border-bottom: none;
}

.batch-item.success {
  color: #28a745;
}

.batch-item.error {
  color: #dc3545;
}

.batch-item.processing {
  color: #007bff;
}
