body {
  width: 350px;
  font-family: sans-serif;
  padding: 10px;
}

label {
  display: block;
  margin-top: 10px;
  font-size: 0.9em;
}

input[type='url'],
input[type='password'],
input[type='text'] {
  width: 95%;
  padding: 8px;
  margin-top: 3px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

/* 书签位置配置样式 */
.bookmark-location-container {
  margin: 15px 0;
  padding: 15px;
  background-color: #f0f8ff;
  border-radius: 6px;
  border: 1px solid #b3d9ff;
}

.bookmark-location-container h3 {
  margin: 0 0 15px 0;
  font-size: 1em;
  color: #0066cc;
  border-bottom: 1px solid #b3d9ff;
  padding-bottom: 5px;
}

.parent-folder-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 3px;
}

.parent-folder-selector select {
  flex: 1;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
}

.refresh-btn {
  padding: 8px 12px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  min-width: auto;
  width: auto;
  margin: 0;
}

.refresh-btn:hover {
  background-color: #218838;
}

.folder-preview {
  margin-top: 10px;
  padding: 8px;
  background-color: #e7f3ff;
  border-radius: 4px;
  border: 1px solid #b3d9ff;
  font-size: 0.9em;
}

.preview-label {
  font-weight: bold;
  color: #0066cc;
}

#folderPreview {
  color: #333;
  font-family: monospace;
}

/* 开关样式 */
.switch-container {
  margin: 15px 0;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.switch-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  cursor: pointer;
  font-size: 0.9em;
}

.switch-text {
  flex: 1;
  margin-right: 10px;
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #007bff;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

input:focus + .slider {
  box-shadow: 0 0 1px #007bff;
}

button {
  display: block;
  width: 100%;
  padding: 10px;
  margin-top: 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
}

button:hover {
  background-color: #0056b3;
}

#batchClassifyBtn {
  background-color: #28a745;
  margin-top: 10px;
}

#batchClassifyBtn:hover {
  background-color: #218838;
}

#cancelBtn {
  background-color: #dc3545;
  margin-top: 10px;
}

#cancelBtn:hover {
  background-color: #c82333;
}

#statusMessage {
  font-size: 0.9em;
  color: red;
  margin-top: 10px;
  min-height: 20px;
}

/* 批量处理进度样式 */
#batchProgress {
  margin-top: 15px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.9em;
  font-weight: bold;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background-color: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
}

#progressFill {
  height: 100%;
  background-color: #007bff;
  width: 0%;
  transition: width 0.3s ease;
}

.batch-details {
  max-height: 150px;
  overflow-y: auto;
  font-size: 0.8em;
  line-height: 1.4;
}

.batch-item {
  padding: 2px 0;
  border-bottom: 1px solid #eee;
}

.batch-item:last-child {
  border-bottom: none;
}

.batch-item.success {
  color: #28a745;
}

.batch-item.error {
  color: #dc3545;
}

.batch-item.processing {
  color: #007bff;
}
