<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>AI 书签分类配置</title>
  <link rel="stylesheet" type="text/css" href="popup.css">
</head>
<body>
  <h1>AI 书签分类</h1>
  <div>
    <label for="apiUrl">API 地址:</label>
    <input type="url" id="apiUrl" name="apiUrl" placeholder="API 地址">
  </div>
  <div>
    <label for="apiKey">API 密钥:</label>
    <input type="password" id="apiKey" name="apiKey" placeholder="API 密钥">
  </div>
  <div>
    <label for="modelName">模型名称:</label>
    <input type="text" id="modelName" name="modelName" placeholder="模型名称 (可选)">
  </div>

  <!-- 书签位置配置 -->
  <div class="bookmark-location-container">
    <h3>书签位置设置</h3>
    <div>
      <label for="bookmarkFolderName">主文件夹名称:</label>
      <input type="text" id="bookmarkFolderName" name="bookmarkFolderName" placeholder="AI分类书签" value="AI分类书签">
    </div>
    <div>
      <label for="parentFolder">父目录:</label>
      <div class="parent-folder-selector">
        <select id="parentFolder" name="parentFolder">
          <option value="">根目录</option>
        </select>
        <button type="button" id="refreshFolders" class="refresh-btn" title="刷新文件夹列表">🔄</button>
      </div>
    </div>
    <div class="folder-preview">
      <span class="preview-label">预览路径:</span>
      <span id="folderPreview">根目录 > AI分类书签</span>
    </div>
  </div>

  <!-- 自动关闭标签页开关 -->
  <div class="switch-container">
    <label for="autoCloseTab" class="switch-label">
      <span class="switch-text">成功收藏后自动关闭标签页</span>
      <div class="switch">
        <input type="checkbox" id="autoCloseTab" name="autoCloseTab">
        <span class="slider"></span>
      </div>
    </label>
  </div>

  <button id="classifyBtn">智能分类并收藏当前页面</button>
  <button id="batchClassifyBtn">批量分类所有标签页</button>
  <button id="cancelBtn" style="display: none;">取消批量操作</button>

  <div id="statusMessage" style="min-height: 20px; margin-top: 10px;"></div>

  <!-- 批量处理进度显示 -->
  <div id="batchProgress" style="display: none;">
    <div class="progress-header">
      <span>批量处理进度</span>
      <span id="progressText">0/0</span>
    </div>
    <div class="progress-bar">
      <div id="progressFill"></div>
    </div>
    <div id="batchDetails" class="batch-details"></div>
  </div>
  <script src="popup.js" defer></script>
</body>
</html>
