<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <h1>AI API 测试工具</h1>
    <p>这个工具可以帮助您测试不同的AI API配置，确保API调用正常工作。</p>
    
    <form id="testForm">
        <div class="form-group">
            <label for="apiType">API 类型:</label>
            <select id="apiType" onchange="updateForm()">
                <option value="google">Google Generative Language API</option>
                <option value="openai">OpenAI 兼容 API</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="apiUrl">API 地址:</label>
            <input type="url" id="apiUrl" placeholder="输入API地址" required>
        </div>
        
        <div class="form-group">
            <label for="apiKey">API 密钥:</label>
            <input type="password" id="apiKey" placeholder="输入API密钥" required>
        </div>
        
        <div class="form-group">
            <label for="modelName">模型名称:</label>
            <input type="text" id="modelName" placeholder="输入模型名称" required>
        </div>
        
        <div class="form-group">
            <label for="testTitle">测试网站标题:</label>
            <input type="text" id="testTitle" value="GitHub - 面向开发者的代码托管平台" placeholder="输入要测试的网站标题">
        </div>
        
        <button type="submit">测试 API</button>
    </form>
    
    <div id="result" class="result" style="display: none;">
        <h3>测试结果:</h3>
        <div id="resultContent"></div>
    </div>

    <script>
        function updateForm() {
            const apiType = document.getElementById('apiType').value;
            const apiUrlInput = document.getElementById('apiUrl');
            const modelNameInput = document.getElementById('modelName');
            
            if (apiType === 'google') {
                apiUrlInput.placeholder = 'https://generativelanguage.googleapis.com';
                apiUrlInput.value = 'https://generativelanguage.googleapis.com';
                modelNameInput.placeholder = 'gemini-2.0-flash';
                modelNameInput.value = 'gemini-2.0-flash';
            } else {
                apiUrlInput.placeholder = 'https://api.openai.com/v1/chat/completions';
                apiUrlInput.value = 'https://api.openai.com/v1/chat/completions';
                modelNameInput.placeholder = 'gpt-3.5-turbo';
                modelNameInput.value = 'gpt-3.5-turbo';
            }
        }
        
        // 初始化表单
        updateForm();
        
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const apiType = document.getElementById('apiType').value;
            const apiUrl = document.getElementById('apiUrl').value;
            const apiKey = document.getElementById('apiKey').value;
            const modelName = document.getElementById('modelName').value;
            const testTitle = document.getElementById('testTitle').value;
            
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            resultDiv.style.display = 'block';
            resultContent.innerHTML = '<p>正在测试API...</p>';
            resultDiv.className = 'result';
            
            try {
                const userPrompt = `请为以下网站标题提供一个简洁的中文分类名称（例如：新闻、科技、购物、教育、娱乐等）。请只返回1到3个字的分类名称，不要任何其他解释、标点符号或句子。网站标题："${testTitle}"`;
                
                let payload, headers, finalApiUrl;
                
                if (apiType === 'google') {
                    finalApiUrl = `${apiUrl}/v1beta/models/${modelName}:generateContent?key=${apiKey}`;
                    payload = {
                        contents: [{
                            parts: [{
                                text: userPrompt
                            }]
                        }],
                        generationConfig: {
                            maxOutputTokens: 15,
                            temperature: 0.3
                        }
                    };
                    headers = {
                        'Content-Type': 'application/json'
                    };
                } else {
                    finalApiUrl = apiUrl;
                    payload = {
                        model: modelName,
                        messages: [
                            { role: "user", content: userPrompt }
                        ],
                        max_tokens: 15,
                        temperature: 0.3
                    };
                    headers = {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    };
                }
                
                console.log('发送请求到:', finalApiUrl);
                console.log('请求头:', headers);
                console.log('请求体:', JSON.stringify(payload, null, 2));
                
                const response = await fetch(finalApiUrl, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(payload)
                });
                
                const responseText = await response.text();
                console.log('响应状态:', response.status);
                console.log('响应文本:', responseText);
                
                if (!response.ok) {
                    throw new Error(`API 请求失败，状态码：${response.status}. ${responseText}`);
                }
                
                const data = JSON.parse(responseText);
                
                let category;
                if (apiType === 'google') {
                    category = data.candidates && data.candidates[0] && data.candidates[0].content && 
                               data.candidates[0].content.parts && data.candidates[0].content.parts[0] && 
                               data.candidates[0].content.parts[0].text;
                } else {
                    category = data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content;
                }
                
                resultDiv.className = 'result success';
                resultContent.innerHTML = `
                    <p><strong>✅ API 调用成功!</strong></p>
                    <p><strong>提取的分类:</strong> ${category || '未能提取'}</p>
                    <p><strong>完整响应:</strong></p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('API 测试错误:', error);
                resultDiv.className = 'result error';
                resultContent.innerHTML = `
                    <p><strong>❌ API 调用失败:</strong></p>
                    <p>${error.message}</p>
                `;
            }
        });
    </script>
</body>
</html>
