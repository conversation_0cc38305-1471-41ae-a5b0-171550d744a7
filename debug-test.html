<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动关闭标签页功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
        }
        .warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            color: #856404;
        }
        .success {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            color: #721c24;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <h1>🔧 自动关闭标签页功能测试指南</h1>
    
    <div class="test-section warning">
        <h3>⚠️ 测试前准备</h3>
        <p>在开始测试之前，请确保：</p>
        <ul>
            <li>已经重新加载了扩展程序</li>
            <li>API配置正确且可用</li>
            <li>打开了开发者工具的Console标签页查看日志</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>📋 测试步骤</h3>
        
        <div class="step">
            <h4>步骤1: 检查开关状态</h4>
            <p>1. 点击扩展图标打开弹窗</p>
            <p>2. 查看"成功收藏后自动关闭标签页"开关</p>
            <p>3. 切换开关状态，观察Console是否有日志输出：</p>
            <div class="debug-info">
                Auto close tab switch changed to: true/false<br>
                Auto close tab setting saved: true/false
            </div>
        </div>

        <div class="step">
            <h4>步骤2: 测试单页分类</h4>
            <p>1. 打开一个测试网页（如这个页面）</p>
            <p>2. 确保开关处于<strong>开启</strong>状态</p>
            <p>3. 点击"智能分类并收藏当前页面"</p>
            <p>4. 观察Console日志，应该看到：</p>
            <div class="debug-info">
                Sending classify message with autoCloseTab: true<br>
                Auto close tab setting: true, Tab ID: [数字]<br>
                Tab [数字] closed automatically after bookmarking
            </div>
            <p>5. 如果成功，当前标签页应该会自动关闭</p>
        </div>

        <div class="step">
            <h4>步骤3: 测试批量分类</h4>
            <p>1. 打开多个测试网页</p>
            <p>2. 确保开关处于<strong>开启</strong>状态</p>
            <p>3. 点击"批量分类所有标签页"</p>
            <p>4. 观察Console日志，每个成功处理的标签页都应该有：</p>
            <div class="debug-info">
                Batch: Auto close tab setting: true, Tab ID: [数字]<br>
                Batch: Tab [数字] closed automatically after bookmarking
            </div>
            <p>5. 成功收藏的标签页应该会逐个自动关闭</p>
        </div>
    </div>

    <div class="test-section">
        <h3>🐛 常见问题排查</h3>
        
        <div class="step error">
            <h4>问题1: 开关状态没有保存</h4>
            <p><strong>症状：</strong>切换开关后重新打开弹窗，开关状态恢复到默认值</p>
            <p><strong>检查：</strong>Console中是否有保存错误信息</p>
            <p><strong>解决：</strong>检查扩展权限，确保有storage权限</p>
        </div>

        <div class="step error">
            <h4>问题2: 配置没有传递到background</h4>
            <p><strong>症状：</strong>Console显示 <code>Auto close tab setting: undefined</code></p>
            <p><strong>检查：</strong>popup.js中是否正确读取了开关状态</p>
            <p><strong>解决：</strong>确保autoCloseTab变量正确获取</p>
        </div>

        <div class="step error">
            <h4>问题3: 标签页关闭失败</h4>
            <p><strong>症状：</strong>Console显示关闭失败的错误信息</p>
            <p><strong>检查：</strong>是否有tabs权限，标签页ID是否有效</p>
            <p><strong>解决：</strong>检查manifest.json中的权限配置</p>
        </div>
    </div>

    <div class="test-section success">
        <h3>✅ 预期行为</h3>
        <ul>
            <li><strong>开关开启时：</strong>成功收藏后自动关闭对应标签页</li>
            <li><strong>开关关闭时：</strong>成功收藏后保持标签页打开</li>
            <li><strong>收藏失败时：</strong>无论开关状态如何，都不关闭标签页</li>
            <li><strong>关闭失败时：</strong>书签仍然成功创建，只是标签页保持打开</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>🔍 调试信息收集</h3>
        <p>如果功能仍然不正常，请收集以下信息：</p>
        <ol>
            <li>打开Chrome开发者工具 (F12)</li>
            <li>切换到Console标签页</li>
            <li>执行测试操作</li>
            <li>复制所有相关的日志信息</li>
            <li>检查是否有错误信息（红色文字）</li>
        </ol>
        
        <div class="debug-info">
            <strong>关键日志关键词：</strong><br>
            - Auto close tab switch changed<br>
            - Auto close tab setting saved<br>
            - Sending classify message with autoCloseTab<br>
            - Auto close tab setting: [true/false]<br>
            - Tab [ID] closed automatically<br>
            - Failed to close tab
        </div>
    </div>

    <div class="test-section">
        <h3>📝 测试用例</h3>
        <p>这个页面可以作为测试用例使用：</p>
        <ul>
            <li>标题：自动关闭标签页功能测试</li>
            <li>URL：以http开头，符合收藏条件</li>
            <li>可以安全关闭，不会影响重要工作</li>
        </ul>
        <p><strong>建议：</strong>复制这个页面的URL，打开多个相同的标签页进行批量测试。</p>
    </div>

    <script>
        // 添加一些客户端调试信息
        console.log('Debug test page loaded');
        console.log('Page title:', document.title);
        console.log('Page URL:', window.location.href);
        
        // 监听页面卸载事件，确认标签页是否被关闭
        window.addEventListener('beforeunload', function(e) {
            console.log('Page is being unloaded - tab might be closing');
        });
    </script>
</body>
</html>
