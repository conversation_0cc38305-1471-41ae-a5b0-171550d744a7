# AI 智能书签分类器 - 测试指南

## 1. 简介

本指南旨在帮助用户和测试人员全面测试“AI 智能书签分类器”浏览器扩展的功能。

此扩展程序使用AI（通过用户配置的API）自动为网页标题生成分类，并将书签保存在以这些分类命名的文件夹中。测试应涵盖使用模拟AI（用于隔离测试）和真实AI服务（如果可用，用于端到端测试）的场景。

## 2. 先决条件

-   **浏览器**: Google Chrome 或 Microsoft Edge 最新版本。
-   **扩展文件**: 包含 `manifest.json`, `popup.html`, `background.js` 等文件的扩展源代码。
-   **(可选，但强烈推荐)**:
    -   访问一个 OpenAI 兼容的 AI API 服务，需要提供 API URL、API 密钥和模型名称。
    -   或者，一个本地运行的模拟 API 服务器（参见下文设置方法）。

## 3. 加载扩展程序 (Unpacked)

### Google Chrome:

1.  打开 Chrome 浏览器。
2.  在地址栏输入 `chrome://extensions` 并回车。
3.  在页面右上角，打开 “**开发者模式**” (Developer mode) 开关。
4.  点击左上角的 “**加载已解压的扩展程序**” (Load unpacked) 按钮。
5.  在文件选择对话框中，选择包含扩展文件（例如 `manifest.json` 所在）的根目录。
6.  扩展程序图标应出现在浏览器工具栏中。

### Microsoft Edge:

1.  打开 Edge 浏览器。
2.  在地址栏输入 `edge://extensions` 并回车。
3.  在页面左下角，打开 “**开发人员模式**” (Developer mode) 开关。
4.  点击 “**加载解压缩的**” (Load unpacked) 按钮（通常位于页面主要区域的右上方）。
5.  在文件选择对话框中，选择包含扩展文件（例如 `manifest.json` 所在）的根目录。
6.  扩展程序图标应出现在浏览器工具栏中。

## 4. 设置模拟 AI 服务器 (可选但推荐)

**为什么使用模拟服务器?**
使用模拟服务器允许您在不依赖外部 AI 服务的情况下测试扩展的核心逻辑。这有助于隔离问题：是扩展本身的问题，还是 AI 服务（如网络、认证、响应格式）的问题。它还可以让您精确控制 AI 返回的类别，以便于测试特定场景。

**简单的 Node.js/Express 模拟服务器示例:**

```javascript
// mock-api-server.js
const express = require('express');
const app = express();
const port = 3000; // 您可以选择任何可用的端口

app.use(express.json());

app.post('/mock-ai', (req, res) => {
  console.log('Mock AI received request:', req.body);
  // 从请求中提取标题信息 (模拟真实API的prompt结构)
  const userMessage = req.body.messages && req.body.messages.find(m => m.role === 'user');
  const content = userMessage && userMessage.content;
  
  let category = "测试综合"; // 默认模拟分类

  if (content) {
    if (content.toLowerCase().includes("news") || content.toLowerCase().includes("新闻")) {
      category = "测试新闻";
    } else if (content.toLowerCase().includes("tech") || content.toLowerCase().includes("科技")) {
      category = "测试科技";
    } else if (content.toLowerCase().includes("shop") || content.toLowerCase().includes("购物")) {
      category = "测试购物";
    }
  }

  // 模拟AI API的响应结构
  res.json({
    choices: [
      {
        message: {
          content: category
        }
      }
    ]
  });
});

app.listen(port, () => {
  console.log(`Mock AI server listening at http://localhost:${port}/mock-ai`);
});
```

**运行模拟服务器:**

1.  确保您已安装 Node.js 和 npm。
2.  在包含 `mock-api-server.js` 文件的目录中打开终端。
3.  安装 `express` (如果尚未安装): `npm install express`
4.  运行服务器: `node mock-api-server.js`
5.  您现在可以在扩展的弹出窗口中将 API URL 设置为 `http://localhost:3000/mock-ai`，API Key 和 Model Name 可以填写任意值（因为模拟服务器不会验证它们，但扩展本身要求非空）。

## 5. 测试用例

### 5.1. 配置

**TC1.1 (保存配置)**
1.  打开扩展弹出窗口。
2.  在 "API 地址" 输入框中输入有效的 API URL (例如 `http://localhost:3000/mock-ai` 或您的真实 API 地址)。
3.  在 "API 密钥" 输入框中输入您的 API 密钥 (或模拟密钥 "testkey")。
4.  在 "模型名称" 输入框中输入模型名称 (例如 "gpt-3.5-turbo" 或模拟模型 "testmodel")。
5.  关闭弹出窗口。
6.  重新打开弹出窗口。
7.  **预期结果:** 所有输入的值应仍然存在于对应的输入框中。

**TC1.2 (无效输入 - 缺少 API 地址)**
1.  打开扩展弹出窗口。
2.  清除 "API 地址" 输入框。
3.  填写 "API 密钥" 和 "模型名称"。
4.  点击 “智能分类并收藏” 按钮。
5.  **预期结果:** 弹出窗口的状态消息区域显示: "API 地址、API 密钥或模型名称未配置完整。" (此消息由 `background.js` 的 `callAI` 函数在尝试进行 API 调用前抛出)

**TC1.3 (无效输入 - 缺少 API 密钥)**
1.  打开扩展弹出窗口。
2.  清除 "API 密钥" 输入框。
3.  填写 "API 地址" 和 "模型名称"。
4.  点击 “智能分类并收藏” 按钮。
5.  **预期结果:** 弹出窗口的状态消息区域显示: "API 地址、API 密钥或模型名称未配置完整。"

**TC1.4 (无效输入 - 缺少模型名称)**
1.  打开扩展弹出窗口。
2.  清除 "模型名称" 输入框。
3.  填写 "API 地址" 和 "API 密钥"。
4.  点击 “智能分类并收藏” 按钮。
5.  **预期结果:** 弹出窗口的状态消息区域显示: "API 地址、API 密钥或模型名称未配置完整。"

### 5.2. 分类和书签创建 (正常流程 - 建议首先使用模拟 API)

**TC2.1 (首次分类)**
1.  配置扩展使用模拟 API (例如 URL: `http://localhost:3000/mock-ai`, Key: `test`, Model: `test`)。
2.  导航到一个示例网页，例如打开一个标签页，其标题包含 "News" (例如，您可以手动编辑一个现有书签的标题，或者找到一个真实的新闻网站)。
3.  点击扩展图标打开弹出窗口。
4.  点击 “智能分类并收藏” 按钮。
5.  **预期结果:**
    *   弹出窗口状态消息首先显示类似 `正在为 "页面标题" 获取分类...`。
    *   然后显示 `网站 "页面标题" AI分类为: 测试新闻`。
    *   最后显示 `网站 "页面标题" 已分类到 "测试新闻" 并成功收藏。`
    *   检查浏览器书签：
        *   应创建一个名为 “AI分类书签” 的新书签文件夹 (如果尚不存在)。
        *   在此文件夹内，应创建一个名为 “测试新闻” 的子文件夹。
        *   当前网页应作为书签保存在 “测试新闻” 子文件夹中，书签标题与网页标题一致。

**TC2.2 (不同分类)**
1.  导航到另一个符合模拟服务器 "测试科技" 条件的网页 (例如，页面标题包含 "Tech")。
2.  点击 “智能分类并收藏” 按钮。
3.  **预期结果:**
    *   弹出窗口显示成功的分类和收藏消息，分类为 “测试科技”。
    *   在 “AI分类书签” 文件夹下创建一个新的 “测试科技” 子文件夹。
    *   新书签被添加到 “测试科技” 子文件夹中。

**TC2.3 (已存在的分类)**
1.  导航到第三个网页，该网页也符合 “测试科技” 分类标准。
2.  点击 “智能分类并收藏” 按钮。
3.  **预期结果:**
    *   弹出窗口显示成功的分类和收藏消息。
    *   书签被添加到现有的 “测试科技” 子文件夹中。
    *   不应创建新的 “测试科技” 文件夹。

### 5.3. 错误处理 (根据情况使用模拟或真实 API)

**TC3.1 (网络错误 - 无效 API 地址)**
1.  在扩展弹出窗口中，将 API 地址设置为一个无效或不存在的本地地址 (例如 `http://localhost:9999/no-api`)。
2.  填写有效的 API 密钥和模型名称。
3.  打开一个普通网页，点击 “智能分类并收藏” 按钮。
4.  **预期结果:** 弹出窗口状态消息显示错误，类似: "处理失败: 调用 AI 服务时出错: Failed to fetch" 或 "处理失败: 调用 AI 服务时出错: NetworkError when attempting to fetch resource." (具体错误信息可能因浏览器而异)。

**TC3.2 (API 认证错误 - 401)**
1.  (需要一个能对无效密钥返回 401 的模拟或真实 API)。
2.  在扩展弹出窗口中配置正确的 API 地址和模型名称，但使用一个无效的 API 密钥。
3.  打开一个普通网页，点击 “智能分类并收藏” 按钮。
4.  **预期结果:** 弹出窗口状态消息显示: "处理失败: AI API 请求失败，状态码：401..." (可能包含更多来自API的错误信息)。

**TC3.3 (API 服务器错误 - 500)**
1.  (需要一个能返回 500 错误的模拟或真实 API)。
2.  在扩展弹出窗口中配置正确的 API 信息，但 API 服务器返回 500 内部错误。
3.  打开一个普通网页，点击 “智能分类并收藏” 按钮。
4.  **预期结果:** 弹出窗口状态消息显示: "处理失败: AI API 请求失败，状态码：500..." (可能包含更多来自API的错误信息)。

**TC3.4 (API 响应错误 - 格式不正确/结构错误)**
1.  (需要一个模拟 API，使其返回无效的 JSON 或与预期不符的响应结构)。
    *   例如，模拟服务器返回 `{"unexpected_field": "some_value"}` 而不是 `{"choices": [...]}`。
2.  打开一个普通网页，点击 “智能分类并收藏” 按钮。
3.  **预期结果:** 弹出窗口状态消息显示: "处理失败: 未能从 AI 响应中提取分类名称。请检查API响应结构。" 或 "处理失败: 调用 AI 服务时出错: Unexpected token..." (如果JSON无效)。

**TC3.5 (API 返回空的分类名称)**
1.  (需要一个模拟 API，使其返回类似 `{"choices": [{"message": {"content": "  "}}]} ` 或 `{"choices": [{"message": {"content": "..."}}]}` 这样的响应，即内容在清理后为空)。
2.  打开一个普通网页，点击 “智能分类并收藏” 按钮。
3.  **预期结果:** 弹出窗口状态消息显示: "处理失败: AI返回的分类名称处理后为空。"

**TC3.6 (不可创建书签的 URL)**
1.  导航到 Chrome 内部页面，例如 `chrome://extensions` 或 `chrome://newtab`。
2.  点击 “智能分类并收藏” 按钮。
3.  **预期结果:** 弹出窗口状态消息显示: "无法为 "扩展程序"（非网页链接）创建书签。" (或其他对应页面的标题)。

### 5.4. 边界情况

**TC4.1 (非常长的标题)**
1.  找到或创建一个标题非常长的网页。
2.  使用模拟或真实 API 进行分类。
3.  点击 “智能分类并收藏” 按钮。
4.  **预期结果:**
    *   分类过程应正常完成。
    *   创建的书签标题应为该非常长的标题。
    *   AI服务本身可能会对长标题进行截断处理以生成分类，这是正常的。

**TC4.2 (标题中包含特殊字符)**
1.  找到或创建一个标题包含各种特殊字符（例如 `" ' ! @ # $ % ^ & * ( ) < > ? / \ | { } [] ; :`，以及表情符号）的网页。
2.  使用模拟或真实 API 进行分类。
3.  点击 “智能分类并收藏” 按钮。
4.  **预期结果:**
    *   分类过程应正常完成。
    *   创建的书签标题应尽可能保留原始特殊字符。
    *   AI 服务在处理时可能会忽略或转换某些特殊字符，这取决于AI模型。扩展本身不应因特殊字符而出错。

**TC4.3 (重复分类同一页面)**
1.  导航到一个网页。
2.  点击 “智能分类并收藏” 按钮，等待操作完成。
3.  不离开当前页面，再次点击 “智能分类并收藏” 按钮。
4.  **预期结果:**
    *   第二个书签应在同一个分类文件夹中创建。
    *   这是当前设计的行为（不进行重复书签检查）。

## 6. 浏览器兼容性清单 (简要)

以下是一些关键测试用例，建议在主要支持的浏览器上执行：

-   [ ] **TC1.1 (保存配置)** - Chrome
-   [ ] **TC1.1 (保存配置)** - Edge
-   [ ] **TC2.1 (首次分类 - 使用模拟 API)** - Chrome
-   [ ] **TC2.1 (首次分类 - 使用模拟 API)** - Edge
-   [ ] **TC3.1 (网络错误 - 无效 API 地址)** - Chrome
-   [ ] **TC3.1 (网络错误 - 无效 API 地址)** - Edge
-   [ ] **TC3.6 (不可创建书签的 URL)** - Chrome
-   [ ] **TC3.6 (不可创建书签的 URL)** - Edge

## 7. 报告问题

如果您在测试过程中发现任何问题或与预期结果不符的行为，请记录以下信息：

1.  **测试用例编号**: (例如, TC3.2)
2.  **浏览器及版本**: (例如, Chrome Version 110.0.5481.177)
3.  **操作系统**: (例如, Windows 11, macOS Ventura)
4.  **重现步骤**: 清晰地描述如何重现该问题。
5.  **预期结果**: 根据测试用例，此步骤应发生什么。
6.  **实际结果**: 实际发生了什么，包括任何错误消息的精确文本。
7.  **截图/录屏**: (如果适用) 有助于直观展示问题。
8.  **API 类型**: 是使用模拟 API 还是特定真实 API (如果是真实 API，请注明服务商，不要泄露密钥)。

将这些信息提交给开发团队或在项目的 Issues 跟踪系统中记录。
